#!/usr/bin/env python3
"""
文生动作演示脚本 - 启动已训练好的模型并生成动作序列
"""

import os
import sys
import torch
import numpy as np
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
import json
import warnings
warnings.filterwarnings('ignore')

# 设置环境变量
os.environ["TOKENIZERS_PARALLELISM"] = "false"

def setup_environment():
    """设置环境和路径"""
    print("设置环境...")
    
    # 切换到 Evaluator_272 目录
    os.chdir('Evaluator_272')
    sys.path.insert(0, os.getcwd())
    
    # 创建必要的符号链接（如果不存在）
    links = [
        ('../utils', './utils'),
        ('../humanml3d_272', './humanml3d_272'),
        ('../options', './options'),
        ('../models', './models'),
        ('../visualization', './visualization'),
        ('../Causal_TAE', './Causal_TAE')
    ]
    
    for src, dst in links:
        if not os.path.exists(dst):
            try:
                if os.name == 'nt':  # Windows
                    os.system(f'mklink /D "{dst}" "{src}"')
                else:  # Unix/Linux
                    os.symlink(src, dst)
                print(f"创建链接: {src} -> {dst}")
            except:
                print(f"警告: 无法创建链接 {src} -> {dst}")

def load_models():
    """加载已训练的模型"""
    print("加载模型...")
    
    # 导入必要的模块
    from sentence_transformers import SentenceTransformer
    from models.llama_model import LLaMAHF, LLaMAHFConfig
    import options.option_transformer as option_trans
    import models.tae as tae
    
    # 获取参数
    args = option_trans.get_args_parser()
    torch.manual_seed(args.seed)
    
    # 设置设备
    comp_device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {comp_device}")
    
    # 加载文本编码器
    print("加载文本编码器...")
    t5_model = SentenceTransformer('../sentencet5-xxl/')
    t5_model.eval()
    for p in t5_model.parameters():
        p.requires_grad = False
    
    # 加载 Causal TAE
    print("加载 Causal TAE...")
    clip_range = [-30, 20]
    net = tae.Causal_HumanTAE(
        hidden_size=args.hidden_size,
        down_t=args.down_t,
        stride_t=args.stride_t,
        depth=args.depth,
        dilation_growth_rate=args.dilation_growth_rate,
        activation='relu',
        latent_dim=args.latent_dim,
        clip_range=clip_range
    )
    
    # 加载TAE权重
    tae_checkpoint = '../Causal_TAE/net_last.pth'
    if os.path.exists(tae_checkpoint):
        print(f'加载TAE检查点: {tae_checkpoint}')
        ckpt = torch.load(tae_checkpoint, map_location='cpu')
        net.load_state_dict(ckpt['net'], strict=True)
        net.eval()
        net.to(comp_device)
    else:
        print(f"警告: TAE检查点不存在: {tae_checkpoint}")
    
    # 加载Transformer
    print("加载Transformer...")
    config = LLaMAHFConfig.from_name('Normal_size')
    config.block_size = 78
    trans_encoder = LLaMAHF(config, args.num_diffusion_head_layers, args.latent_dim, comp_device)
    
    # 尝试加载Transformer权重（如果存在）
    trans_checkpoint = args.resume_trans
    if trans_checkpoint and os.path.exists(trans_checkpoint):
        print(f'加载Transformer检查点: {trans_checkpoint}')
        ckpt = torch.load(trans_checkpoint, map_location='cpu')
        new_ckpt_trans = {}
        for key in ckpt['trans'].keys():
            if key.split('.')[0] == 'module':
                new_key = '.'.join(key.split('.')[1:])
            else:
                new_key = key
            new_ckpt_trans[new_key] = ckpt['trans'][key]
        trans_encoder.load_state_dict(new_ckpt_trans, strict=True)
        trans_encoder.eval()
        trans_encoder.to(comp_device)
    else:
        print("警告: 未找到Transformer检查点，将使用随机初始化的权重")
    
    return net, trans_encoder, t5_model, comp_device, args

def motion_to_joints(motion_data):
    """将272维运动数据转换为关节位置"""
    num_joints = 22
    num_frames = motion_data.shape[0]
    
    # 简化：假设前66维是22个关节的3D位置 (22 * 3 = 66)
    joints_3d = motion_data[:, :66].reshape(num_frames, num_joints, 3)
    return joints_3d

def create_motion_gif(joints_3d, save_path, fps=10, max_frames=60, title=""):
    """创建运动GIF动画"""
    skeleton_connections = [
        (0, 1), (1, 2), (2, 3),  # 脊柱
        (0, 4), (4, 5), (5, 6),  # 左臂
        (0, 7), (7, 8), (8, 9),  # 右臂
        (0, 10), (10, 11), (11, 12),  # 左腿
        (0, 13), (13, 14), (14, 15),  # 右腿
    ]
    
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 计算整个动画的坐标范围
    all_joints = joints_3d.reshape(-1, 3)
    max_range = np.array([all_joints[:, 0].max() - all_joints[:, 0].min(),
                         all_joints[:, 1].max() - all_joints[:, 1].min(),
                         all_joints[:, 2].max() - all_joints[:, 2].min()]).max() / 2.0
    mid_x = (all_joints[:, 0].max() + all_joints[:, 0].min()) * 0.5
    mid_y = (all_joints[:, 1].max() + all_joints[:, 1].min()) * 0.5
    mid_z = (all_joints[:, 2].max() + all_joints[:, 2].min()) * 0.5
    
    def animate(frame):
        ax.clear()
        frame_joints = joints_3d[frame]
        
        # 绘制关节点
        ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
                   c='red', s=50, alpha=0.8)
        
        # 绘制骨架连接
        for connection in skeleton_connections:
            if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
                start_joint = frame_joints[connection[0]]
                end_joint = frame_joints[connection[1]]
                ax.plot([start_joint[0], end_joint[0]], 
                       [start_joint[1], end_joint[1]], 
                       [start_joint[2], end_joint[2]], 'b-', linewidth=2)
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(f'{title}\nFrame {frame}')
    
    # 创建动画
    num_frames = min(max_frames, len(joints_3d))
    ani = animation.FuncAnimation(fig, animate, frames=num_frames, interval=1000//fps, repeat=True)
    
    ani.save(save_path, writer='pillow', fps=fps)
    print(f"动画已保存到: {save_path}")
    plt.close()
    return ani

def generate_motion_from_text(text_prompt, net, trans_encoder, t5_model, comp_device, max_length=120):
    """从文本生成动作序列"""
    print(f"生成动作: {text_prompt}")
    
    # 编码文本
    with torch.no_grad():
        text_embedding = t5_model.encode([text_prompt])
        text_embedding = torch.from_numpy(text_embedding).float().to(comp_device)
        
        # 这里应该使用transformer生成动作序列
        # 由于没有完整的生成代码，我们使用一个简化的示例
        # 在实际应用中，这里需要实现完整的文本到动作的生成逻辑
        
        # 生成随机的潜在向量作为示例
        batch_size = 1
        latent_dim = 16
        seq_len = max_length // 4  # 考虑下采样
        
        # 生成随机潜在向量（实际应该由transformer生成）
        latent_motion = torch.randn(batch_size, seq_len, latent_dim).to(comp_device)
        
        # 使用TAE解码
        decoded_motion = net.decode(latent_motion)
        
        # 转换为numpy
        motion_data = decoded_motion[0].cpu().numpy()
        
    return motion_data

def demo_with_sample_texts():
    """使用示例文本进行演示"""
    # 示例文本
    sample_texts = [
        "a person walks forward slowly",
        "a person is running fast",
        "a person jumps up and down",
        "a person is dancing",
        "a person waves their hand"
    ]
    
    try:
        # 设置环境
        setup_environment()
        
        # 加载模型
        net, trans_encoder, t5_model, comp_device, args = load_models()
        
        # 创建输出目录
        os.makedirs('../demo_output', exist_ok=True)
        
        print("\n开始生成动作序列...")
        
        for i, text in enumerate(sample_texts):
            print(f"\n处理文本 {i+1}/{len(sample_texts)}: {text}")
            
            try:
                # 生成动作
                motion_data = generate_motion_from_text(text, net, trans_encoder, t5_model, comp_device)
                
                print(f"生成的动作数据形状: {motion_data.shape}")
                
                # 转换为关节位置
                joints_3d = motion_to_joints(motion_data)
                print(f"关节数据形状: {joints_3d.shape}")
                
                # 创建动画
                if len(joints_3d) > 5:
                    anim_path = f'../demo_output/motion_{i+1:03d}.gif'
                    create_motion_gif(joints_3d, save_path=anim_path, fps=10, max_frames=30, title=text)
                    print(f"动画已保存: {anim_path}")
                
            except Exception as e:
                print(f"处理文本时出错: {e}")
                continue
        
        print(f"\n演示完成！生成的动画保存在 demo_output 目录中")
        
    except Exception as e:
        print(f"演示过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== 文生动作演示系统 ===")
    print("正在启动...")
    demo_with_sample_texts()
