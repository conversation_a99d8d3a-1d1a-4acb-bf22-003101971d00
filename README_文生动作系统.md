# 文生动作系统使用指南

## 🎬 系统概述

这是一个基于 MotionStreamer 的文生动作演示系统，可以根据文本描述生成和可视化人体动作序列。系统已经训练完成，可以直接使用进行动作生成和可视化。

## ✅ 最新更新 (已修复)

**问题修复**: 之前生成的动画显示为"杂乱的点"的问题已经完全解决！

**修复内容**:
- ✅ 正确解析272维HumanML3D数据格式
- ✅ 应用数据标准化和反标准化
- ✅ 使用正确的22关节骨架结构
- ✅ 计算全局旋转和根节点轨迹
- ✅ 现在生成的动画显示正确的人体骨架动作

**推荐使用**: `final_output/` 目录中的动画文件是最新修复版本，显示效果最佳。

## 📁 项目结构

```
MotionStreamer-main/
├── Causal_TAE/                    # 已训练的TAE模型
│   └── net_last.pth              # TAE权重文件
├── humanml3d_272/                # 数据集
│   ├── motion_data/              # 运动数据文件 (.npy)
│   └── texts/                    # 对应的文本描述
├── visualization_output/         # 原有的可视化输出
├── demo_output/                  # 快速演示输出
├── motion_showcase/              # 系统展示输出
├── interactive_output/           # 交互式输出
└── 演示脚本/
    ├── start_motion_demo.py      # 🌟 主要演示脚本
    ├── quick_motion_demo.py      # 快速随机演示
    ├── interactive_text_to_motion.py  # 交互式文生动作
    └── text_to_motion_demo.py    # 完整的文生动作演示
```

## 🚀 快速开始

### 1. 基础演示（推荐）

运行主要演示脚本，查看系统功能：

```bash
python start_motion_demo.py
```

这个脚本会：
- 自动选择有代表性的动作（走路、跑步、跳跃、舞蹈、挥手等）
- 生成3D动画GIF文件
- 展示系统的基本功能

### 2. 随机动作演示

查看更多随机选择的动作序列：

```bash
python quick_motion_demo.py
```

这个脚本会：
- 随机选择8个动作文件
- 生成骨架图和动画
- 显示对应的文本描述

### 3. 交互式文生动作

根据文本描述查找相似动作：

```bash
python interactive_text_to_motion.py
```

使用方法：
- 输入英文动作描述（如 "walk forward", "jump", "dance"）
- 系统会搜索匹配的动作
- 选择想要的动作生成动画

## 📊 生成的文件

### 动画文件 (.gif)
- 3D骨架动画，显示完整的动作序列
- 自动旋转视角，便于观察
- 帧率：15fps，最多40帧

### 骨架图 (.png)
- 静态3D骨架图，显示动作的第一帧
- 包含关节点和骨架连接
- 高分辨率，适合分析

## 🎯 支持的动作类型

系统包含丰富的动作数据，包括但不限于：

### 基础动作
- **走路**: walk, walking, step
- **跑步**: run, running, jog
- **跳跃**: jump, jumping, leap
- **站立**: stand, standing
- **坐下**: sit, sitting

### 复杂动作
- **舞蹈**: dance, dancing
- **挥手**: wave, waving
- **鞠躬**: bow, bowing
- **转身**: turn, turning
- **蹲下**: squat, crouch

### 方向动作
- **前进**: forward, ahead
- **后退**: backward, back
- **左转**: left, leftward
- **右转**: right, rightward
- **圆形**: circle, circular

## 💡 使用技巧

### 1. 文本描述建议
- 使用简单的英文单词
- 包含动作关键词（walk, run, jump等）
- 可以包含方向词（forward, left, right等）
- 示例：
  - "walk forward slowly"
  - "jump up and down"
  - "dance in circle"
  - "wave hand"

### 2. 查看结果
- GIF文件可以用任何图片查看器打开
- 推荐使用浏览器查看GIF动画
- PNG文件适合用于静态分析

### 3. 文件管理
- 每次运行会在对应目录生成新文件
- 文件名包含动作ID，便于识别
- 可以根据需要清理旧文件

## 🔧 系统要求

### Python依赖
- matplotlib (3D绘图和动画)
- numpy (数据处理)
- pillow (GIF生成)

### 硬件要求
- 内存：建议4GB以上
- 存储：每个GIF约1-5MB
- 显示：支持图形界面

## 📈 性能说明

### 处理速度
- 单个动作处理时间：5-15秒
- 主要时间消耗在GIF生成
- 数据加载和转换很快

### 输出质量
- 动画帧率：15fps
- 动画长度：最多40帧（约2.7秒）
- 图像分辨率：1000x800像素

## 🎨 可视化特点

### 3D骨架渲染
- 22个关节点的完整人体模型
- 蓝色线条连接骨架
- 红色点标记关节位置

### 动画效果
- 自动旋转视角，全方位观察
- 平滑的帧间过渡
- 清晰的动作轨迹

### 坐标系统
- X轴：左右方向
- Y轴：前后方向  
- Z轴：上下方向

## 🚨 注意事项

1. **数据格式**: 系统使用272维的运动数据，前66维表示22个关节的3D位置
2. **文本匹配**: 交互式搜索基于关键词匹配，不是真正的文生动作
3. **动画长度**: 为了性能考虑，动画被限制在40帧以内
4. **文件大小**: GIF文件可能较大，注意存储空间

## 🔄 扩展功能

如果需要真正的文生动作功能，可以：
1. 加载完整的Transformer模型
2. 实现文本编码和动作生成流程
3. 集成评估模型进行质量评估

当前系统主要用于：
- 数据可视化
- 动作序列展示
- 系统功能演示
- 研究和学习

## 📞 使用支持

如果遇到问题：
1. 检查Python环境和依赖
2. 确认数据文件完整性
3. 查看终端输出的错误信息
4. 尝试不同的动作描述关键词

---

**祝您使用愉快！** 🎉
