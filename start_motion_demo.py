#!/usr/bin/env python3
"""
文生动作系统启动脚本
展示系统的基本功能
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from glob import glob
import random
import torch
import torch.nn.functional as F

def motion_to_joints(motion_data):
    """正确解析272维运动数据为关节位置"""

    # 加载均值和标准差进行反标准化
    try:
        mean = np.load('humanml3d_272/mean_std/Mean.npy')
        std = np.load('humanml3d_272/mean_std/Std.npy')
        motion_data = motion_data * std + mean
    except:
        pass  # 如果无法加载，使用原始数据

    nfrm, _ = motion_data.shape
    njoint = 22

    # 根据HumanML3D 272维数据格式解析
    positions_no_heading = motion_data[:, 8:8+3*njoint].reshape(nfrm, -1, 3)
    velocities_root_xy_no_heading = motion_data[:, :2]
    global_heading_diff_rot = motion_data[:, 2:8]

    def rotation_6d_to_matrix(d6):
        """将6D旋转表示转换为旋转矩阵"""
        d6 = torch.from_numpy(d6).float()
        a1, a2 = d6[..., :3], d6[..., 3:]
        b1 = F.normalize(a1, dim=-1)
        b2 = a2 - (b1 * a2).sum(-1, keepdim=True) * b1
        b2 = F.normalize(b2, dim=-1)
        b3 = torch.cross(b1, b2, dim=-1)
        return torch.stack((b1, b2, b3), dim=-2).numpy()

    # 计算全局旋转
    rotation_matrices = rotation_6d_to_matrix(global_heading_diff_rot)

    # 累积旋转
    accumulated_rotations = [rotation_matrices[0]]
    for i in range(1, len(rotation_matrices)):
        accumulated_rotations.append(np.matmul(rotation_matrices[i], accumulated_rotations[-1]))
    accumulated_rotations = np.array(accumulated_rotations)

    # 应用旋转到局部位置
    positions_with_heading = np.zeros_like(positions_no_heading)
    for i in range(nfrm):
        positions_with_heading[i] = np.matmul(positions_no_heading[i], accumulated_rotations[i].T)

    # 计算根节点轨迹
    velocities_root_xyz_no_heading = np.zeros((nfrm, 3))
    velocities_root_xyz_no_heading[:, [0, 2]] = velocities_root_xy_no_heading

    # 应用旋转到根节点速度
    for i in range(nfrm):
        velocities_root_xyz_no_heading[i] = np.matmul(velocities_root_xyz_no_heading[i], accumulated_rotations[i].T)

    # 累积根节点位置
    root_translation = np.cumsum(velocities_root_xyz_no_heading, axis=0)
    positions_with_heading[:, :, 0] += root_translation[:, 0:1]
    positions_with_heading[:, :, 2] += root_translation[:, 2:]

    return positions_with_heading

def create_motion_gif(joints_3d, save_path, fps=15, max_frames=30, title=""):
    """创建运动GIF动画"""
    # HumanML3D 22关节骨架连接关系
    skeleton_connections = [
        # 脊柱链
        (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
        # 左臂链
        (4, 7), (7, 8), (8, 9), (9, 10),
        # 右臂链
        (4, 11), (11, 12), (12, 13), (13, 14),
        # 左腿链
        (0, 15), (15, 16), (16, 17), (17, 18),
        # 右腿链
        (0, 19), (19, 20), (20, 21)
    ]
    
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    all_joints = joints_3d.reshape(-1, 3)
    max_range = np.array([all_joints[:, 0].max() - all_joints[:, 0].min(),
                         all_joints[:, 1].max() - all_joints[:, 1].min(),
                         all_joints[:, 2].max() - all_joints[:, 2].min()]).max() / 2.0
    
    if max_range < 0.1:
        max_range = 1.0
    
    mid_x = (all_joints[:, 0].max() + all_joints[:, 0].min()) * 0.5
    mid_y = (all_joints[:, 1].max() + all_joints[:, 1].min()) * 0.5
    mid_z = (all_joints[:, 2].max() + all_joints[:, 2].min()) * 0.5
    
    def animate(frame):
        ax.clear()
        frame_joints = joints_3d[frame]
        
        ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
                   c='red', s=60, alpha=0.8)
        
        for connection in skeleton_connections:
            if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
                start_joint = frame_joints[connection[0]]
                end_joint = frame_joints[connection[1]]
                ax.plot([start_joint[0], end_joint[0]], 
                       [start_joint[1], end_joint[1]], 
                       [start_joint[2], end_joint[2]], 'b-', linewidth=2)
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(f'{title}\nFrame {frame+1}', fontsize=12)
        ax.view_init(elev=20, azim=frame * 2)
    
    num_frames = min(max_frames, len(joints_3d))
    ani = animation.FuncAnimation(fig, animate, frames=num_frames, interval=1000//fps, repeat=True)
    
    try:
        ani.save(save_path, writer='pillow', fps=fps)
        print(f"✓ 动画已保存: {save_path}")
        return True
    except Exception as e:
        print(f"✗ 保存失败: {e}")
        return False
    finally:
        plt.close()

def load_text_description(motion_id):
    """加载文本描述"""
    text_file = f"humanml3d_272/texts/{motion_id}.txt"
    if os.path.exists(text_file):
        try:
            with open(text_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    desc = lines[0].strip()
                    if '#' in desc:
                        desc = desc.split('#')[0]
                    return desc
        except:
            pass
    return "No description available"

def demo_showcase():
    """展示系统功能"""
    print("🎬 文生动作系统演示")
    print("=" * 50)
    
    # 创建输出目录
    os.makedirs('motion_showcase', exist_ok=True)
    
    # 获取一些有代表性的动作
    motion_files = glob("humanml3d_272/motion_data/*.npy")
    
    # 选择一些有趣的动作进行展示
    showcase_motions = []
    keywords_to_find = ['walk', 'run', 'jump', 'dance', 'wave', 'bow']
    
    for keyword in keywords_to_find:
        for motion_file in motion_files:
            motion_id = os.path.basename(motion_file).replace('.npy', '')
            text_desc = load_text_description(motion_id).lower()
            
            if keyword in text_desc:
                showcase_motions.append((motion_file, motion_id, text_desc))
                break
    
    # 如果没找到足够的，随机选择一些
    if len(showcase_motions) < 3:
        random_files = random.sample(motion_files, min(5, len(motion_files)))
        for motion_file in random_files:
            motion_id = os.path.basename(motion_file).replace('.npy', '')
            text_desc = load_text_description(motion_id)
            showcase_motions.append((motion_file, motion_id, text_desc))
    
    print(f"📁 准备展示 {len(showcase_motions)} 个动作序列")
    print()
    
    for i, (motion_file, motion_id, text_desc) in enumerate(showcase_motions[:5], 1):
        print(f"🎭 处理动作 {i}: {motion_id}")
        print(f"   📝 描述: {text_desc}")
        
        try:
            # 加载运动数据
            motion_data = np.load(motion_file)
            joints_3d = motion_to_joints(motion_data)
            
            print(f"   📊 数据形状: {motion_data.shape} -> {joints_3d.shape}")
            
            # 生成动画
            if len(joints_3d) > 5:
                anim_path = f'motion_showcase/motion_{i}_{motion_id}.gif'
                success = create_motion_gif(joints_3d, save_path=anim_path, 
                                          fps=15, max_frames=25, title=text_desc)
                if success:
                    print(f"   ✅ 动画生成成功")
                else:
                    print(f"   ❌ 动画生成失败")
            else:
                print(f"   ⚠️  序列太短，跳过")
            
            print()
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            print()
    
    print("🎉 演示完成！")
    print("📂 生成的动画文件保存在 motion_showcase 目录中")
    print()
    
    # 显示生成的文件
    output_files = glob("motion_showcase/*.gif")
    if output_files:
        print("📋 生成的动画文件:")
        for file in sorted(output_files):
            print(f"   • {os.path.basename(file)}")
    
    print()
    print("💡 使用说明:")
    print("   1. 运行 python interactive_text_to_motion.py 进行交互式体验")
    print("   2. 运行 python quick_motion_demo.py 查看更多随机动作")
    print("   3. 查看 visualization_output 目录中已有的可视化结果")

if __name__ == "__main__":
    demo_showcase()
