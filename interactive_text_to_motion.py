#!/usr/bin/env python3
"""
交互式文生动作演示系统
用户可以输入文本描述来查看相似的动作序列
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from glob import glob
import random
import re
import torch
import torch.nn.functional as F

def motion_to_joints(motion_data):
    """正确解析272维运动数据为关节位置"""

    # 加载均值和标准差进行反标准化
    try:
        mean = np.load('humanml3d_272/mean_std/Mean.npy')
        std = np.load('humanml3d_272/mean_std/Std.npy')
        motion_data = motion_data * std + mean
    except:
        pass  # 如果无法加载，使用原始数据

    nfrm, _ = motion_data.shape
    njoint = 22

    # 根据HumanML3D 272维数据格式解析
    positions_no_heading = motion_data[:, 8:8+3*njoint].reshape(nfrm, -1, 3)
    velocities_root_xy_no_heading = motion_data[:, :2]
    global_heading_diff_rot = motion_data[:, 2:8]

    def rotation_6d_to_matrix(d6):
        """将6D旋转表示转换为旋转矩阵"""
        d6 = torch.from_numpy(d6).float()
        a1, a2 = d6[..., :3], d6[..., 3:]
        b1 = F.normalize(a1, dim=-1)
        b2 = a2 - (b1 * a2).sum(-1, keepdim=True) * b1
        b2 = F.normalize(b2, dim=-1)
        b3 = torch.cross(b1, b2, dim=-1)
        return torch.stack((b1, b2, b3), dim=-2).numpy()

    # 计算全局旋转
    rotation_matrices = rotation_6d_to_matrix(global_heading_diff_rot)

    # 累积旋转
    accumulated_rotations = [rotation_matrices[0]]
    for i in range(1, len(rotation_matrices)):
        accumulated_rotations.append(np.matmul(rotation_matrices[i], accumulated_rotations[-1]))
    accumulated_rotations = np.array(accumulated_rotations)

    # 应用旋转到局部位置
    positions_with_heading = np.zeros_like(positions_no_heading)
    for i in range(nfrm):
        positions_with_heading[i] = np.matmul(positions_no_heading[i], accumulated_rotations[i].T)

    # 计算根节点轨迹
    velocities_root_xyz_no_heading = np.zeros((nfrm, 3))
    velocities_root_xyz_no_heading[:, [0, 2]] = velocities_root_xy_no_heading

    # 应用旋转到根节点速度
    for i in range(nfrm):
        velocities_root_xyz_no_heading[i] = np.matmul(velocities_root_xyz_no_heading[i], accumulated_rotations[i].T)

    # 累积根节点位置
    root_translation = np.cumsum(velocities_root_xyz_no_heading, axis=0)
    positions_with_heading[:, :, 0] += root_translation[:, 0:1]
    positions_with_heading[:, :, 2] += root_translation[:, 2:]

    return positions_with_heading

def create_motion_gif(joints_3d, save_path, fps=15, max_frames=40, title=""):
    """创建运动GIF动画"""
    # HumanML3D 22关节骨架连接关系
    skeleton_connections = [
        # 脊柱链
        (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
        # 左臂链
        (4, 7), (7, 8), (8, 9), (9, 10),
        # 右臂链
        (4, 11), (11, 12), (12, 13), (13, 14),
        # 左腿链
        (0, 15), (15, 16), (16, 17), (17, 18),
        # 右腿链
        (0, 19), (19, 20), (20, 21)
    ]
    
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    
    # 计算整个动画的坐标范围
    all_joints = joints_3d.reshape(-1, 3)
    max_range = np.array([all_joints[:, 0].max() - all_joints[:, 0].min(),
                         all_joints[:, 1].max() - all_joints[:, 1].min(),
                         all_joints[:, 2].max() - all_joints[:, 2].min()]).max() / 2.0
    
    if max_range < 0.1:
        max_range = 1.0
    
    mid_x = (all_joints[:, 0].max() + all_joints[:, 0].min()) * 0.5
    mid_y = (all_joints[:, 1].max() + all_joints[:, 1].min()) * 0.5
    mid_z = (all_joints[:, 2].max() + all_joints[:, 2].min()) * 0.5
    
    def animate(frame):
        ax.clear()
        frame_joints = joints_3d[frame]
        
        # 绘制关节点
        ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
                   c='red', s=60, alpha=0.8, edgecolors='darkred')
        
        # 绘制骨架连接
        for connection in skeleton_connections:
            if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
                start_joint = frame_joints[connection[0]]
                end_joint = frame_joints[connection[1]]
                ax.plot([start_joint[0], end_joint[0]], 
                       [start_joint[1], end_joint[1]], 
                       [start_joint[2], end_joint[2]], 'b-', linewidth=2, alpha=0.7)
        
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        ax.set_xlabel('X')
        ax.set_ylabel('Y')
        ax.set_zlabel('Z')
        ax.set_title(f'{title}\nFrame {frame+1}/{len(joints_3d)}', fontsize=12)
        
        # 设置视角
        ax.view_init(elev=20, azim=frame * 3)
    
    # 创建动画
    num_frames = min(max_frames, len(joints_3d))
    ani = animation.FuncAnimation(fig, animate, frames=num_frames, interval=1000//fps, repeat=True)
    
    try:
        ani.save(save_path, writer='pillow', fps=fps)
        print(f"✓ 动画已保存到: {save_path}")
        return True
    except Exception as e:
        print(f"✗ 保存动画失败: {e}")
        return False
    finally:
        plt.close()

def load_text_description(motion_id):
    """加载对应的文本描述"""
    text_file = f"humanml3d_272/texts/{motion_id}.txt"
    if os.path.exists(text_file):
        try:
            with open(text_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 返回第一行作为主要描述，去掉标注信息
                if lines:
                    desc = lines[0].strip()
                    # 提取#之前的部分作为纯文本描述
                    if '#' in desc:
                        desc = desc.split('#')[0]
                    return desc
                return "No description"
        except:
            return "Failed to load description"
    return "No description available"

def search_motions_by_keywords(keywords, max_results=5):
    """根据关键词搜索相关的动作"""
    motion_files = glob("humanml3d_272/motion_data/*.npy")
    matching_motions = []
    
    keywords_lower = [kw.lower() for kw in keywords]
    
    for motion_file in motion_files:
        motion_id = os.path.basename(motion_file).replace('.npy', '')
        text_desc = load_text_description(motion_id).lower()
        
        # 检查是否包含任何关键词
        if any(keyword in text_desc for keyword in keywords_lower):
            matching_motions.append((motion_file, motion_id, text_desc))
        
        if len(matching_motions) >= max_results:
            break
    
    return matching_motions

def display_motion_options(matching_motions):
    """显示匹配的动作选项"""
    print("\n🎯 找到以下匹配的动作:")
    print("=" * 60)
    
    for i, (motion_file, motion_id, text_desc) in enumerate(matching_motions, 1):
        print(f"{i}. ID: {motion_id}")
        print(f"   描述: {text_desc}")
        print(f"   文件: {os.path.basename(motion_file)}")
        print()

def generate_motion_animation(motion_file, motion_id, text_desc, output_dir="interactive_output"):
    """生成指定动作的动画"""
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 加载运动数据
        motion_data = np.load(motion_file)
        print(f"📊 运动数据形状: {motion_data.shape}")
        
        # 转换为关节位置
        joints_3d = motion_to_joints(motion_data)
        print(f"🦴 关节数据形状: {joints_3d.shape}")
        
        # 创建动画
        if len(joints_3d) > 5:
            anim_path = f'{output_dir}/motion_{motion_id}.gif'
            success = create_motion_gif(joints_3d, save_path=anim_path, fps=15, max_frames=30, 
                                      title=f"{text_desc}")
            if success:
                return anim_path
        else:
            print(f"⚠️  序列太短 ({len(joints_3d)} 帧)，跳过动画生成")
            
    except Exception as e:
        print(f"❌ 处理失败: {e}")
    
    return None

def main():
    print("🎬 交互式文生动作演示系统")
    print("=" * 50)
    print("输入文本描述来查找相似的动作序列")
    print("输入 'quit' 或 'exit' 退出程序")
    print("=" * 50)
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n💬 请输入动作描述 (英文): ").strip()
            
            if user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            
            if not user_input:
                print("⚠️  请输入有效的描述")
                continue
            
            # 提取关键词
            keywords = re.findall(r'\b\w+\b', user_input.lower())
            print(f"🔍 搜索关键词: {keywords}")
            
            # 搜索匹配的动作
            matching_motions = search_motions_by_keywords(keywords, max_results=5)
            
            if not matching_motions:
                print("😔 没有找到匹配的动作，请尝试其他关键词")
                print("💡 建议尝试: walk, run, jump, dance, sit, stand, wave, bow 等")
                continue
            
            # 显示选项
            display_motion_options(matching_motions)
            
            # 让用户选择
            try:
                choice = input("请选择要生成动画的动作 (输入数字 1-{}): ".format(len(matching_motions)))
                choice_idx = int(choice) - 1
                
                if 0 <= choice_idx < len(matching_motions):
                    motion_file, motion_id, text_desc = matching_motions[choice_idx]
                    
                    print(f"\n🎭 正在生成动作动画...")
                    print(f"📝 描述: {text_desc}")
                    
                    anim_path = generate_motion_animation(motion_file, motion_id, text_desc)
                    
                    if anim_path:
                        print(f"🎉 动画生成成功！")
                        print(f"📁 文件位置: {anim_path}")
                        print(f"💡 你可以用图片查看器或浏览器打开这个GIF文件")
                    else:
                        print("😔 动画生成失败")
                        
                else:
                    print("⚠️  无效的选择")
                    
            except ValueError:
                print("⚠️  请输入有效的数字")
                
        except KeyboardInterrupt:
            print("\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")

if __name__ == "__main__":
    main()
