#!/usr/bin/env python3
"""
文生动作系统主启动脚本
提供多种演示模式选择
"""

import os
import sys
import subprocess

def print_banner():
    """打印系统横幅"""
    print("=" * 60)
    print("🎬 文生动作系统 (Text-to-Motion System)")
    print("基于 MotionStreamer 的动作生成与可视化系统")
    print("✅ 已修复272维数据解析问题，现在显示正确的骨架动作！")
    print("=" * 60)
    print()

def print_menu():
    """打印菜单选项"""
    print("📋 请选择演示模式:")
    print()
    print("1. 🌟 系统功能展示 (推荐)")
    print("   - 展示走路、跑步、跳跃、舞蹈等经典动作")
    print("   - 生成高质量3D动画")
    print("   - 适合首次体验")
    print()
    print("2. 🎲 随机动作演示")
    print("   - 随机选择8个动作序列")
    print("   - 显示对应文本描述")
    print("   - 生成骨架图和动画")
    print()
    print("3. 💬 交互式文生动作")
    print("   - 输入文本描述搜索相似动作")
    print("   - 支持关键词匹配")
    print("   - 个性化动作选择")
    print()
    print("4. 📁 查看已生成的动画")
    print("   - 浏览现有的可视化结果")
    print("   - 查看文件列表和描述")
    print()
    print("5. 📖 查看使用说明")
    print("   - 详细的系统介绍")
    print("   - 使用技巧和注意事项")
    print()
    print("0. 🚪 退出系统")
    print()

def run_script(script_name):
    """运行指定的Python脚本"""
    try:
        print(f"🚀 启动 {script_name}...")
        print("-" * 40)
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=False, 
                              text=True)
        print("-" * 40)
        if result.returncode == 0:
            print(f"✅ {script_name} 执行完成")
        else:
            print(f"❌ {script_name} 执行出错")
    except Exception as e:
        print(f"❌ 运行 {script_name} 时出错: {e}")

def show_generated_files():
    """显示已生成的文件"""
    print("📁 已生成的动画文件:")
    print("=" * 40)
    
    directories = [
        ("final_output", "最终修复版本 (推荐)"),
        ("motion_showcase", "系统功能展示"),
        ("demo_output", "快速演示输出"),
        ("test_output", "测试输出"),
        ("interactive_output", "交互式输出"),
        ("visualization_output", "原有可视化")
    ]
    
    total_files = 0
    
    for dir_name, description in directories:
        if os.path.exists(dir_name):
            files = [f for f in os.listdir(dir_name) if f.endswith(('.gif', '.png'))]
            if files:
                print(f"\n📂 {description} ({dir_name}/):")
                for file in sorted(files):
                    print(f"   • {file}")
                    total_files += 1
    
    if total_files == 0:
        print("暂无生成的文件，请先运行演示脚本。")
    else:
        print(f"\n📊 总计: {total_files} 个文件")
        print("\n💡 提示: 可以用图片查看器或浏览器打开GIF文件查看动画")

def show_readme():
    """显示使用说明"""
    readme_file = "README_文生动作系统.md"
    if os.path.exists(readme_file):
        print("📖 正在打开使用说明...")
        try:
            # 尝试用默认程序打开markdown文件
            if os.name == 'nt':  # Windows
                os.startfile(readme_file)
            elif os.name == 'posix':  # macOS/Linux
                subprocess.run(['open', readme_file])
            print("✅ 使用说明已在默认程序中打开")
        except:
            # 如果无法打开，则在终端中显示
            print("📄 使用说明内容:")
            print("-" * 40)
            try:
                with open(readme_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 只显示前50行
                    lines = content.split('\n')
                    for line in lines[:50]:
                        print(line)
                    if len(lines) > 50:
                        print("...")
                        print(f"(还有 {len(lines) - 50} 行，请查看完整文件)")
            except Exception as e:
                print(f"❌ 无法读取说明文件: {e}")
    else:
        print("❌ 使用说明文件不存在")

def check_dependencies():
    """检查系统依赖"""
    required_modules = ['matplotlib', 'numpy']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("⚠️  缺少以下Python模块:")
        for module in missing_modules:
            print(f"   • {module}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def main():
    """主函数"""
    print_banner()
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 系统依赖检查失败，请安装缺少的模块后重试")
        return
    
    while True:
        print_menu()
        
        try:
            choice = input("请输入选项编号 (0-5): ").strip()
            print()
            
            if choice == '0':
                print("👋 感谢使用文生动作系统，再见！")
                break
            
            elif choice == '1':
                run_script('start_motion_demo.py')
                
            elif choice == '2':
                run_script('quick_motion_demo.py')
                
            elif choice == '3':
                print("💡 提示: 在交互模式中，输入 'quit' 或 'exit' 返回主菜单")
                run_script('interactive_text_to_motion.py')
                
            elif choice == '4':
                show_generated_files()
                
            elif choice == '5':
                show_readme()
                
            else:
                print("⚠️  无效的选项，请输入 0-5 之间的数字")
            
            if choice in ['1', '2', '3', '4', '5']:
                input("\n按回车键继续...")
                print("\n" + "=" * 60 + "\n")
                
        except KeyboardInterrupt:
            print("\n\n👋 程序被用户中断，再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
