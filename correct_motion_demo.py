#!/usr/bin/env python3
"""
正确的运动数据解析和可视化脚本
基于官方实现修复数据解析问题
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from glob import glob
import random
import torch
import torch.nn.functional as F

def rotation_6d_to_matrix(d6: torch.Tensor) -> torch.Tensor:
    """将6D旋转表示转换为旋转矩阵"""
    a1, a2 = d6[..., :3], d6[..., 3:]
    b1 = F.normalize(a1, dim=-1)
    b2 = a2 - (b1 * a2).sum(-1, keepdim=True) * b1
    b2 = F.normalize(b2, dim=-1)
    b3 = torch.cross(b1, b2, dim=-1)
    return torch.stack((b1, b2, b3), dim=-2)

def accumulate_rotations(relative_rotations):
    """累积旋转矩阵"""
    R_total = [relative_rotations[0]]
    for R_rel in relative_rotations[1:]:
        R_total.append(np.matmul(R_rel, R_total[-1]))
    return np.array(R_total)

def recover_from_local_position_correct(final_x, njoint=22):
    """
    正确的272维数据恢复函数
    基于官方实现 utils/eval_trans.py
    """
    if final_x.ndim == 3:
        bs, nfrm, _ = final_x.shape
        is_batched = True
    else:
        nfrm, _ = final_x.shape
        bs = 1
        is_batched = False
        final_x = final_x.reshape(1, *final_x.shape)

    # 解析数据
    positions_no_heading = final_x[:,:,8:8+3*njoint].reshape(bs, nfrm, njoint, 3) 
    velocities_root_xy_no_heading = final_x[:,:,:2] 
    global_heading_diff_rot = final_x[:,:,2:8] 

    positions_with_heading = []
    for b in range(bs):
        # 计算累积旋转
        global_heading_rot = accumulate_rotations(
            rotation_6d_to_matrix(torch.from_numpy(global_heading_diff_rot[b])).numpy()
        )
        inv_global_heading_rot = np.transpose(global_heading_rot, (0, 2, 1))
        
        # 应用旋转到关节位置
        curr_pos_with_heading = np.matmul(
            np.repeat(inv_global_heading_rot[:, None,:, :], njoint, axis=1), 
            positions_no_heading[b][...,None]
        ).squeeze(-1)

        # 处理根节点速度
        velocities_root_xyz_no_heading = np.zeros((velocities_root_xy_no_heading[b].shape[0], 3))
        velocities_root_xyz_no_heading[:, 0] = velocities_root_xy_no_heading[b, :, 0]
        velocities_root_xyz_no_heading[:, 2] = velocities_root_xy_no_heading[b, :, 1]
        velocities_root_xyz_no_heading[1:, :] = np.matmul(
            inv_global_heading_rot[:-1], 
            velocities_root_xyz_no_heading[1:, :,None]
        ).squeeze(-1)

        # 累积根节点位移
        root_translation = np.cumsum(velocities_root_xyz_no_heading, axis=0)

        # 添加根节点位移到关节位置
        curr_pos_with_heading[:, :, 0] += root_translation[:, 0:1]
        curr_pos_with_heading[:, :, 2] += root_translation[:, 2:]
        
        positions_with_heading.append(curr_pos_with_heading)

    positions_with_heading = np.stack(positions_with_heading, axis=0)

    if not is_batched:
        positions_with_heading = positions_with_heading.squeeze(0)

    return positions_with_heading

def motion_to_joints_official(motion_data):
    """使用官方方法将272维运动数据转换为关节位置"""
    
    # 加载均值和标准差进行反标准化
    try:
        mean = np.load('humanml3d_272/mean_std/Mean.npy')
        std = np.load('humanml3d_272/mean_std/Std.npy')
        print(f"✓ 成功加载标准化参数: mean {mean.shape}, std {std.shape}")
        
        # 反标准化
        motion_data = motion_data * std + mean
        print(f"✓ 完成反标准化")
    except Exception as e:
        print(f"⚠️  无法加载标准化参数: {e}")
        print("使用原始数据继续...")
    
    # 使用官方恢复函数
    joints_3d = recover_from_local_position_correct(motion_data, njoint=22)
    
    print(f"恢复的关节位置形状: {joints_3d.shape}")
    print(f"位置范围: X[{joints_3d[:,:,0].min():.2f}, {joints_3d[:,:,0].max():.2f}], "
          f"Y[{joints_3d[:,:,1].min():.2f}, {joints_3d[:,:,1].max():.2f}], "
          f"Z[{joints_3d[:,:,2].min():.2f}, {joints_3d[:,:,2].max():.2f}]")
    
    return joints_3d

def create_motion_gif_official(joints_3d, save_path, fps=15, max_frames=30, title=""):
    """创建运动GIF动画 - 官方版本"""
    # HumanML3D 22关节骨架连接关系
    skeleton_connections = [
        # 脊柱链
        (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
        # 左臂链
        (4, 7), (7, 8), (8, 9), (9, 10),
        # 右臂链  
        (4, 11), (11, 12), (12, 13), (13, 14),
        # 左腿链
        (0, 15), (15, 16), (16, 17), (17, 18),
        # 右腿链
        (0, 19), (19, 20), (20, 21)
    ]
    
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # 计算整个动画的坐标范围
    all_joints = joints_3d.reshape(-1, 3)
    
    # 使用更合理的范围计算
    x_range = all_joints[:, 0].max() - all_joints[:, 0].min()
    y_range = all_joints[:, 1].max() - all_joints[:, 1].min()
    z_range = all_joints[:, 2].max() - all_joints[:, 2].min()
    max_range = max(x_range, y_range, z_range) / 2.0
    
    # 确保最小范围
    if max_range < 1.0:
        max_range = 1.0
    
    mid_x = (all_joints[:, 0].max() + all_joints[:, 0].min()) * 0.5
    mid_y = (all_joints[:, 1].max() + all_joints[:, 1].min()) * 0.5
    mid_z = (all_joints[:, 2].max() + all_joints[:, 2].min()) * 0.5
    
    print(f"动画范围: 中心({mid_x:.2f}, {mid_y:.2f}, {mid_z:.2f}), 半径{max_range:.2f}")
    
    def animate(frame):
        ax.clear()
        frame_joints = joints_3d[frame]
        
        # 绘制关节点
        ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
                   c='red', s=80, alpha=0.9, edgecolors='darkred')
        
        # 绘制骨架连接
        for connection in skeleton_connections:
            if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
                start_joint = frame_joints[connection[0]]
                end_joint = frame_joints[connection[1]]
                ax.plot([start_joint[0], end_joint[0]], 
                       [start_joint[1], end_joint[1]], 
                       [start_joint[2], end_joint[2]], 'b-', linewidth=3, alpha=0.8)
        
        # 设置坐标轴
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        ax.set_xlabel('X (meters)', fontsize=12)
        ax.set_ylabel('Y (meters)', fontsize=12)
        ax.set_zlabel('Z (meters)', fontsize=12)
        ax.set_title(f'{title}\nFrame {frame+1}/{len(joints_3d)}', fontsize=14)
        
        # 设置视角 - 固定视角更容易观察
        ax.view_init(elev=15, azim=45)
    
    # 创建动画
    num_frames = min(max_frames, len(joints_3d))
    ani = animation.FuncAnimation(fig, animate, frames=num_frames, interval=1000//fps, repeat=True)
    
    try:
        ani.save(save_path, writer='pillow', fps=fps)
        print(f"✓ 动画已保存: {save_path}")
        return True
    except Exception as e:
        print(f"✗ 保存失败: {e}")
        return False
    finally:
        plt.close()

def load_text_description(motion_id):
    """加载文本描述"""
    text_file = f"humanml3d_272/texts/{motion_id}.txt"
    if os.path.exists(text_file):
        try:
            with open(text_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    desc = lines[0].strip()
                    if '#' in desc:
                        desc = desc.split('#')[0]
                    return desc
        except:
            pass
    return "No description available"

def official_demo():
    """官方实现演示"""
    print("🎬 基于官方实现的运动数据解析演示")
    print("=" * 60)
    print("✅ 使用 utils/eval_trans.py 中的官方恢复函数")
    print("✅ 正确处理旋转矩阵和根节点轨迹")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs('official_output', exist_ok=True)
    
    # 选择测试动作
    test_motions = ["000005", "000031", "000106"]
    
    for i, motion_id in enumerate(test_motions, 1):
        motion_file = f"humanml3d_272/motion_data/{motion_id}.npy"
        
        if not os.path.exists(motion_file):
            print(f"⚠️  文件不存在: {motion_file}")
            continue
            
        print(f"\n🎭 处理动作 {i}: {motion_id}")
        
        # 加载文本描述
        text_desc = load_text_description(motion_id)
        print(f"   📝 描述: {text_desc}")
        
        try:
            # 加载运动数据
            motion_data = np.load(motion_file)
            print(f"   📊 原始数据形状: {motion_data.shape}")
            
            # 使用官方方法恢复关节位置
            joints_3d = motion_to_joints_official(motion_data)
            print(f"   🦴 关节数据形状: {joints_3d.shape}")
            
            # 生成动画
            if len(joints_3d) > 5:
                anim_path = f'official_output/official_motion_{i}_{motion_id}.gif'
                success = create_motion_gif_official(joints_3d, save_path=anim_path, 
                                                   fps=15, max_frames=30, title=text_desc)
                if success:
                    print(f"   ✅ 动画生成成功")
                else:
                    print(f"   ❌ 动画生成失败")
            else:
                print(f"   ⚠️  序列太短，跳过")
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 官方实现演示完成！")
    print(f"📂 生成的动画文件保存在 official_output 目录中")
    
    # 显示生成的文件
    output_files = glob("official_output/*.gif")
    if output_files:
        print("\n📋 生成的动画文件:")
        for file in sorted(output_files):
            print(f"   • {os.path.basename(file)}")
    
    print(f"\n💡 这次应该显示正确的人体骨架动作了！")

if __name__ == "__main__":
    official_demo()
