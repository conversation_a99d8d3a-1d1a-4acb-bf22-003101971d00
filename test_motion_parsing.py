#!/usr/bin/env python3
"""
测试272维运动数据解析是否正确
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn.functional as F

def motion_to_joints_correct(motion_data):
    """正确解析272维运动数据为关节位置"""
    
    # 加载均值和标准差进行反标准化
    try:
        mean = np.load('humanml3d_272/mean_std/Mean.npy')
        std = np.load('humanml3d_272/mean_std/Std.npy')
        
        # 反标准化
        motion_data = motion_data * std + mean
        print(f"✓ 成功加载均值和标准差文件")
    except Exception as e:
        print(f"⚠️  无法加载均值和标准差文件: {e}")
        print("使用原始数据继续...")
    
    # 使用正确的272维数据解析方法
    nfrm, _ = motion_data.shape
    njoint = 22
    
    print(f"数据形状: {motion_data.shape}")
    print(f"帧数: {nfrm}, 关节数: {njoint}")
    
    # 根据HumanML3D 272维数据格式解析
    # 前2维: root velocity (x, z)
    # 2-8维: global heading rotation (6D)
    # 8-74维: 22个关节的局部位置 (22*3=66维)
    
    positions_no_heading = motion_data[:, 8:8+3*njoint].reshape(nfrm, -1, 3)
    velocities_root_xy_no_heading = motion_data[:, :2] 
    global_heading_diff_rot = motion_data[:, 2:8] 
    
    print(f"局部位置形状: {positions_no_heading.shape}")
    print(f"根节点速度形状: {velocities_root_xy_no_heading.shape}")
    print(f"全局旋转形状: {global_heading_diff_rot.shape}")
    
    def rotation_6d_to_matrix(d6):
        """将6D旋转表示转换为旋转矩阵"""
        d6 = torch.from_numpy(d6).float()
        a1, a2 = d6[..., :3], d6[..., 3:]
        b1 = F.normalize(a1, dim=-1)
        b2 = a2 - (b1 * a2).sum(-1, keepdim=True) * b1
        b2 = F.normalize(b2, dim=-1)
        b3 = torch.cross(b1, b2, dim=-1)
        return torch.stack((b1, b2, b3), dim=-2).numpy()
    
    # 计算全局旋转
    rotation_matrices = rotation_6d_to_matrix(global_heading_diff_rot)
    
    # 累积旋转
    accumulated_rotations = [rotation_matrices[0]]
    for i in range(1, len(rotation_matrices)):
        accumulated_rotations.append(np.matmul(rotation_matrices[i], accumulated_rotations[-1]))
    accumulated_rotations = np.array(accumulated_rotations)
    
    # 应用旋转到局部位置
    positions_with_heading = np.zeros_like(positions_no_heading)
    for i in range(nfrm):
        positions_with_heading[i] = np.matmul(positions_no_heading[i], accumulated_rotations[i].T)
    
    # 计算根节点轨迹
    velocities_root_xyz_no_heading = np.zeros((nfrm, 3))
    velocities_root_xyz_no_heading[:, [0, 2]] = velocities_root_xy_no_heading
    
    # 应用旋转到根节点速度
    for i in range(nfrm):
        velocities_root_xyz_no_heading[i] = np.matmul(velocities_root_xyz_no_heading[i], accumulated_rotations[i].T)
    
    # 累积根节点位置
    root_translation = np.cumsum(velocities_root_xyz_no_heading, axis=0)
    positions_with_heading[:, :, 0] += root_translation[:, 0:1]
    positions_with_heading[:, :, 2] += root_translation[:, 2:]
    
    print(f"最终关节位置形状: {positions_with_heading.shape}")
    print(f"位置范围: X[{positions_with_heading[:,:,0].min():.2f}, {positions_with_heading[:,:,0].max():.2f}], "
          f"Y[{positions_with_heading[:,:,1].min():.2f}, {positions_with_heading[:,:,1].max():.2f}], "
          f"Z[{positions_with_heading[:,:,2].min():.2f}, {positions_with_heading[:,:,2].max():.2f}]")
    
    return positions_with_heading

def plot_skeleton_test(joints_3d, frame_idx=0, save_path=None, title=""):
    """绘制3D骨架进行测试"""
    # HumanML3D 22关节骨架连接关系
    skeleton_connections = [
        # 脊柱链
        (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
        # 左臂链
        (4, 7), (7, 8), (8, 9), (9, 10),
        # 右臂链  
        (4, 11), (11, 12), (12, 13), (13, 14),
        # 左腿链
        (0, 15), (15, 16), (16, 17), (17, 18),
        # 右腿链
        (0, 19), (19, 20), (20, 21)
    ]
    
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 获取指定帧的关节位置
    frame_joints = joints_3d[frame_idx]
    
    print(f"第{frame_idx}帧关节位置:")
    for i, pos in enumerate(frame_joints):
        print(f"  关节{i}: ({pos[0]:.3f}, {pos[1]:.3f}, {pos[2]:.3f})")
    
    # 绘制关节点
    ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
               c='red', s=80, alpha=0.8, edgecolors='darkred')
    
    # 标注关节编号
    for i, pos in enumerate(frame_joints):
        ax.text(pos[0], pos[1], pos[2], f'{i}', fontsize=8)
    
    # 绘制骨架连接
    for connection in skeleton_connections:
        if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
            start_joint = frame_joints[connection[0]]
            end_joint = frame_joints[connection[1]]
            ax.plot([start_joint[0], end_joint[0]], 
                   [start_joint[1], end_joint[1]], 
                   [start_joint[2], end_joint[2]], 'b-', linewidth=3, alpha=0.7)
    
    # 设置坐标轴
    ax.set_xlabel('X', fontsize=12)
    ax.set_ylabel('Y', fontsize=12)
    ax.set_zlabel('Z', fontsize=12)
    ax.set_title(f'{title}\nFrame {frame_idx}', fontsize=14, pad=20)
    
    # 设置相同的坐标轴范围
    max_range = np.array([frame_joints[:, 0].max() - frame_joints[:, 0].min(),
                         frame_joints[:, 1].max() - frame_joints[:, 1].min(),
                         frame_joints[:, 2].max() - frame_joints[:, 2].min()]).max() / 2.0
    
    if max_range < 0.1:
        max_range = 1.0
    
    mid_x = (frame_joints[:, 0].max() + frame_joints[:, 0].min()) * 0.5
    mid_y = (frame_joints[:, 1].max() + frame_joints[:, 1].min()) * 0.5
    mid_z = (frame_joints[:, 2].max() + frame_joints[:, 2].min()) * 0.5
    
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 测试骨架图已保存到: {save_path}")
    
    plt.close()
    return fig

def test_motion_parsing():
    """测试运动数据解析"""
    print("🧪 测试272维运动数据解析")
    print("=" * 50)
    
    # 选择一个测试文件
    test_file = "humanml3d_272/motion_data/000005.npy"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    print(f"📁 加载测试文件: {test_file}")
    
    # 加载运动数据
    motion_data = np.load(test_file)
    print(f"原始数据形状: {motion_data.shape}")
    
    # 解析为关节位置
    print("\n🔄 解析运动数据...")
    joints_3d = motion_to_joints_correct(motion_data)
    
    # 创建输出目录
    os.makedirs('test_output', exist_ok=True)
    
    # 绘制几个关键帧
    frames_to_test = [0, len(joints_3d)//4, len(joints_3d)//2, len(joints_3d)*3//4, len(joints_3d)-1]
    
    for i, frame_idx in enumerate(frames_to_test):
        print(f"\n📊 测试第{frame_idx}帧...")
        save_path = f'test_output/test_skeleton_frame_{frame_idx}.png'
        plot_skeleton_test(joints_3d, frame_idx=frame_idx, save_path=save_path, 
                          title=f"Test Frame {frame_idx}")
    
    print(f"\n🎉 测试完成！")
    print(f"📂 测试结果保存在 test_output 目录中")
    
    # 检查数据合理性
    print(f"\n📈 数据合理性检查:")
    print(f"  关节位置范围是否合理: {'✓' if np.abs(joints_3d).max() < 10 else '✗'}")
    print(f"  是否包含NaN值: {'✗' if np.isnan(joints_3d).any() else '✓'}")
    print(f"  关节数量是否正确: {'✓' if joints_3d.shape[1] == 22 else '✗'}")

if __name__ == "__main__":
    test_motion_parsing()
