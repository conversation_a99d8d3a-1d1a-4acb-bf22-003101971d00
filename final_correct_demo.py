#!/usr/bin/env python3
"""
最终正确版本 - 使用正确的HumanML3D关节顺序
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from glob import glob
import torch
import torch.nn.functional as F

def rotation_6d_to_matrix(d6: torch.Tensor) -> torch.Tensor:
    """将6D旋转表示转换为旋转矩阵"""
    a1, a2 = d6[..., :3], d6[..., 3:]
    b1 = F.normalize(a1, dim=-1)
    b2 = a2 - (b1 * a2).sum(-1, keepdim=True) * b1
    b2 = F.normalize(b2, dim=-1)
    b3 = torch.cross(b1, b2, dim=-1)
    return torch.stack((b1, b2, b3), dim=-2)

def accumulate_rotations(relative_rotations):
    """累积旋转矩阵"""
    R_total = [relative_rotations[0]]
    for R_rel in relative_rotations[1:]:
        R_total.append(np.matmul(R_rel, R_total[-1]))
    return np.array(R_total)

def recover_from_local_position_final(final_x, njoint=22):
    """最终正确的272维数据恢复函数"""
    if final_x.ndim == 3:
        bs, nfrm, _ = final_x.shape
        is_batched = True
    else:
        nfrm, _ = final_x.shape
        bs = 1
        is_batched = False
        final_x = final_x.reshape(1, *final_x.shape)

    positions_no_heading = final_x[:,:,8:8+3*njoint].reshape(bs, nfrm, njoint, 3) 
    velocities_root_xy_no_heading = final_x[:,:,:2] 
    global_heading_diff_rot = final_x[:,:,2:8] 

    positions_with_heading = []
    for b in range(bs):
        global_heading_rot = accumulate_rotations(
            rotation_6d_to_matrix(torch.from_numpy(global_heading_diff_rot[b])).numpy()
        )
        inv_global_heading_rot = np.transpose(global_heading_rot, (0, 2, 1))
        
        curr_pos_with_heading = np.matmul(
            np.repeat(inv_global_heading_rot[:, None,:, :], njoint, axis=1), 
            positions_no_heading[b][...,None]
        ).squeeze(-1)

        velocities_root_xyz_no_heading = np.zeros((velocities_root_xy_no_heading[b].shape[0], 3))
        velocities_root_xyz_no_heading[:, 0] = velocities_root_xy_no_heading[b, :, 0]
        velocities_root_xyz_no_heading[:, 2] = velocities_root_xy_no_heading[b, :, 1]
        velocities_root_xyz_no_heading[1:, :] = np.matmul(
            inv_global_heading_rot[:-1], 
            velocities_root_xyz_no_heading[1:, :,None]
        ).squeeze(-1)

        root_translation = np.cumsum(velocities_root_xyz_no_heading, axis=0)
        curr_pos_with_heading[:, :, 0] += root_translation[:, 0:1]
        curr_pos_with_heading[:, :, 2] += root_translation[:, 2:]
        
        positions_with_heading.append(curr_pos_with_heading)

    positions_with_heading = np.stack(positions_with_heading, axis=0)

    if not is_batched:
        positions_with_heading = positions_with_heading.squeeze(0)

    return positions_with_heading

def motion_to_joints_final(motion_data):
    """最终版本的运动数据转换"""
    
    # 加载均值和标准差进行反标准化
    try:
        mean = np.load('humanml3d_272/mean_std/Mean.npy')
        std = np.load('humanml3d_272/mean_std/Std.npy')
        motion_data = motion_data * std + mean
        print(f"✓ 完成反标准化")
    except Exception as e:
        print(f"⚠️  无法加载标准化参数: {e}")
    
    # 使用正确的恢复函数
    joints_3d = recover_from_local_position_final(motion_data, njoint=22)
    
    print(f"恢复的关节位置形状: {joints_3d.shape}")
    print(f"位置范围: X[{joints_3d[:,:,0].min():.2f}, {joints_3d[:,:,0].max():.2f}], "
          f"Y[{joints_3d[:,:,1].min():.2f}, {joints_3d[:,:,1].max():.2f}], "
          f"Z[{joints_3d[:,:,2].min():.2f}, {joints_3d[:,:,2].max():.2f}]")
    
    return joints_3d

def create_motion_gif_final(joints_3d, save_path, fps=15, max_frames=30, title=""):
    """创建最终版本的运动GIF动画"""
    
    # 正确的HumanML3D关节顺序和连接关系
    humanml3d_joints = [
        "root", "RH", "LH", "BP", "RK", "LK", "BT", "RMrot", "LMrot", "BLN",
        "RF", "LF", "BMN", "RSI", "LSI", "BUN", "RS", "LS", "RE", "LE", "RW", "LW"
    ]
    
    # 基于HumanML3D运动学树的连接关系
    # humanml3d_kinematic_tree = [
    #     [0, 3, 6, 9, 12, 15],  # body: root->BP->BT->BLN->BMN->BUN
    #     [9, 14, 17, 19, 21],   # left arm: BLN->LSI->LS->LE->LW  
    #     [9, 13, 16, 18, 20],   # right arm: BLN->RSI->RS->RE->RW
    #     [0, 2, 5, 8, 11],      # left leg: root->LH->LK->LMrot->LF
    #     [0, 1, 4, 7, 10],      # right leg: root->RH->RK->RMrot->RF
    # ]
    
    skeleton_connections = [
        # 身体主干
        (0, 3), (3, 6), (6, 9), (9, 12), (12, 15),  # root->BP->BT->BLN->BMN->BUN(head)
        # 左臂
        (9, 14), (14, 17), (17, 19), (19, 21),      # BLN->LSI->LS->LE->LW
        # 右臂  
        (9, 13), (13, 16), (16, 18), (18, 20),      # BLN->RSI->RS->RE->RW
        # 左腿
        (0, 2), (2, 5), (5, 8), (8, 11),            # root->LH->LK->LMrot->LF
        # 右腿
        (0, 1), (1, 4), (4, 7), (7, 10),            # root->RH->RK->RMrot->RF
    ]
    
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # 计算整个动画的坐标范围
    all_joints = joints_3d.reshape(-1, 3)
    
    x_range = all_joints[:, 0].max() - all_joints[:, 0].min()
    y_range = all_joints[:, 1].max() - all_joints[:, 1].min()
    z_range = all_joints[:, 2].max() - all_joints[:, 2].min()
    max_range = max(x_range, y_range, z_range) / 2.0
    
    # 确保最小范围
    if max_range < 1.0:
        max_range = 1.0
    
    mid_x = (all_joints[:, 0].max() + all_joints[:, 0].min()) * 0.5
    mid_y = (all_joints[:, 1].max() + all_joints[:, 1].min()) * 0.5
    mid_z = (all_joints[:, 2].max() + all_joints[:, 2].min()) * 0.5
    
    print(f"动画范围: 中心({mid_x:.2f}, {mid_y:.2f}, {mid_z:.2f}), 半径{max_range:.2f}")
    
    def animate(frame):
        ax.clear()
        frame_joints = joints_3d[frame]
        
        # 绘制关节点
        ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
                   c='red', s=80, alpha=0.9, edgecolors='darkred')
        
        # 绘制骨架连接
        for connection in skeleton_connections:
            if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
                start_joint = frame_joints[connection[0]]
                end_joint = frame_joints[connection[1]]
                ax.plot([start_joint[0], end_joint[0]], 
                       [start_joint[1], end_joint[1]], 
                       [start_joint[2], end_joint[2]], 'b-', linewidth=3, alpha=0.8)
        
        # 设置坐标轴
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        ax.set_xlabel('X (meters)', fontsize=12)
        ax.set_ylabel('Y (meters)', fontsize=12)
        ax.set_zlabel('Z (meters)', fontsize=12)
        ax.set_title(f'{title}\nFrame {frame+1}/{len(joints_3d)}', fontsize=14)
        
        # 设置视角
        ax.view_init(elev=15, azim=45)
    
    # 创建动画
    num_frames = min(max_frames, len(joints_3d))
    ani = animation.FuncAnimation(fig, animate, frames=num_frames, interval=1000//fps, repeat=True)
    
    try:
        ani.save(save_path, writer='pillow', fps=fps)
        print(f"✓ 动画已保存: {save_path}")
        return True
    except Exception as e:
        print(f"✗ 保存失败: {e}")
        return False
    finally:
        plt.close()

def plot_skeleton_with_correct_labels(joints_3d, frame_idx=0, save_path=None, title=""):
    """绘制带正确标签的骨架"""
    
    # 正确的HumanML3D关节名称
    humanml3d_joints = [
        "root", "RH", "LH", "BP", "RK", "LK", "BT", "RMrot", "LMrot", "BLN",
        "RF", "LF", "BMN", "RSI", "LSI", "BUN", "RS", "LS", "RE", "LE", "RW", "LW"
    ]
    
    skeleton_connections = [
        # 身体主干
        (0, 3), (3, 6), (6, 9), (9, 12), (12, 15),
        # 左臂
        (9, 14), (14, 17), (17, 19), (19, 21),
        # 右臂  
        (9, 13), (13, 16), (16, 18), (18, 20),
        # 左腿
        (0, 2), (2, 5), (5, 8), (8, 11),
        # 右腿
        (0, 1), (1, 4), (4, 7), (7, 10),
    ]
    
    fig = plt.figure(figsize=(15, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    frame_joints = joints_3d[frame_idx]
    
    # 绘制关节点
    ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
               c='red', s=100, alpha=0.8, edgecolors='darkred')
    
    # 标注关节名称
    for i, (pos, name) in enumerate(zip(frame_joints, humanml3d_joints)):
        ax.text(pos[0], pos[1], pos[2], f'{i}:{name}', fontsize=8)
    
    # 绘制骨架连接
    for connection in skeleton_connections:
        if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
            start_joint = frame_joints[connection[0]]
            end_joint = frame_joints[connection[1]]
            ax.plot([start_joint[0], end_joint[0]], 
                   [start_joint[1], end_joint[1]], 
                   [start_joint[2], end_joint[2]], 'b-', linewidth=3, alpha=0.7)
    
    # 设置坐标轴
    ax.set_xlabel('X (meters)', fontsize=12)
    ax.set_ylabel('Y (meters)', fontsize=12)
    ax.set_zlabel('Z (meters)', fontsize=12)
    ax.set_title(f'{title}\nFrame {frame_idx} - HumanML3D Joints', fontsize=14, pad=20)
    
    # 设置合理的坐标轴范围
    max_range = 1.0
    mid_x = frame_joints[:, 0].mean()
    mid_y = frame_joints[:, 1].mean()
    mid_z = frame_joints[:, 2].mean()
    
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    ax.view_init(elev=15, azim=45)
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 正确骨架图已保存: {save_path}")
    
    plt.close()

def load_text_description(motion_id):
    """加载文本描述"""
    text_file = f"humanml3d_272/texts/{motion_id}.txt"
    if os.path.exists(text_file):
        try:
            with open(text_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    desc = lines[0].strip()
                    if '#' in desc:
                        desc = desc.split('#')[0]
                    return desc
        except:
            pass
    return "No description available"

def final_correct_demo():
    """最终正确版本演示"""
    print("🎬 最终正确版本 - HumanML3D关节顺序")
    print("=" * 60)
    print("✅ 使用正确的HumanML3D关节定义")
    print("✅ 基于官方运动学树构建骨架连接")
    print("=" * 60)
    
    # 创建输出目录
    os.makedirs('final_correct_output', exist_ok=True)
    
    # 选择测试动作
    test_motions = ["000005", "000031", "000106"]
    
    for i, motion_id in enumerate(test_motions, 1):
        motion_file = f"humanml3d_272/motion_data/{motion_id}.npy"
        
        if not os.path.exists(motion_file):
            print(f"⚠️  文件不存在: {motion_file}")
            continue
            
        print(f"\n🎭 处理动作 {i}: {motion_id}")
        
        # 加载文本描述
        text_desc = load_text_description(motion_id)
        print(f"   📝 描述: {text_desc}")
        
        try:
            # 加载运动数据
            motion_data = np.load(motion_file)
            print(f"   📊 原始数据形状: {motion_data.shape}")
            
            # 使用最终正确方法恢复关节位置
            joints_3d = motion_to_joints_final(motion_data)
            print(f"   🦴 关节数据形状: {joints_3d.shape}")
            
            # 检查关键距离
            frame_0 = joints_3d[0]
            head_pos = frame_0[15]  # BUN (head)
            foot_pos = frame_0[11]  # LF (left foot)
            head_to_foot_dist = np.linalg.norm(head_pos - foot_pos)
            print(f"   📏 头到脚距离: {head_to_foot_dist:.3f}m")
            
            # 绘制骨架验证图
            skeleton_path = f'final_correct_output/skeleton_{motion_id}_frame_0.png'
            plot_skeleton_with_correct_labels(joints_3d, frame_idx=0, save_path=skeleton_path, 
                                            title=f"Motion {motion_id}: {text_desc}")
            
            # 生成动画
            if len(joints_3d) > 5:
                anim_path = f'final_correct_output/final_correct_motion_{i}_{motion_id}.gif'
                success = create_motion_gif_final(joints_3d, save_path=anim_path, 
                                                fps=15, max_frames=30, title=text_desc)
                if success:
                    print(f"   ✅ 动画生成成功")
                else:
                    print(f"   ❌ 动画生成失败")
            else:
                print(f"   ⚠️  序列太短，跳过")
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n🎉 最终正确版本演示完成！")
    print(f"📂 生成的文件保存在 final_correct_output 目录中")
    
    # 显示生成的文件
    output_files = glob("final_correct_output/*")
    if output_files:
        print("\n📋 生成的文件:")
        for file in sorted(output_files):
            print(f"   • {os.path.basename(file)}")
    
    print(f"\n💡 这次应该显示正确比例的人体骨架动作了！")

if __name__ == "__main__":
    final_correct_demo()
