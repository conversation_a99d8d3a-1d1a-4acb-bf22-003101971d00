#!/usr/bin/env python3
"""
快速动作演示脚本 - 直接使用现有数据进行可视化
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import matplotlib.animation as animation
from glob import glob
import random

def motion_to_joints(motion_data):
    """将272维运动数据转换为关节位置"""
    import torch
    import torch.nn.functional as F

    # 加载均值和标准差进行反标准化
    try:
        mean = np.load('humanml3d_272/mean_std/Mean.npy')
        std = np.load('humanml3d_272/mean_std/Std.npy')

        # 反标准化
        motion_data = motion_data * std + mean
    except:
        print("警告: 无法加载均值和标准差文件，使用原始数据")

    # 使用正确的272维数据解析方法
    nfrm, _ = motion_data.shape
    njoint = 22

    # 根据HumanML3D 272维数据格式解析
    # 前2维: root velocity (x, z)
    # 2-8维: global heading rotation (6D)
    # 8-74维: 22个关节的局部位置 (22*3=66维)
    # 其余维度: 其他特征

    positions_no_heading = motion_data[:, 8:8+3*njoint].reshape(nfrm, -1, 3)
    velocities_root_xy_no_heading = motion_data[:, :2]
    global_heading_diff_rot = motion_data[:, 2:8]

    def rotation_6d_to_matrix(d6):
        """将6D旋转表示转换为旋转矩阵"""
        d6 = torch.from_numpy(d6).float()
        a1, a2 = d6[..., :3], d6[..., 3:]
        b1 = F.normalize(a1, dim=-1)
        b2 = a2 - (b1 * a2).sum(-1, keepdim=True) * b1
        b2 = F.normalize(b2, dim=-1)
        b3 = torch.cross(b1, b2, dim=-1)
        return torch.stack((b1, b2, b3), dim=-2).numpy()

    # 计算全局旋转
    rotation_matrices = rotation_6d_to_matrix(global_heading_diff_rot)

    # 累积旋转
    accumulated_rotations = [rotation_matrices[0]]
    for i in range(1, len(rotation_matrices)):
        accumulated_rotations.append(np.matmul(rotation_matrices[i], accumulated_rotations[-1]))
    accumulated_rotations = np.array(accumulated_rotations)

    # 应用旋转到局部位置
    positions_with_heading = np.zeros_like(positions_no_heading)
    for i in range(nfrm):
        positions_with_heading[i] = np.matmul(positions_no_heading[i], accumulated_rotations[i].T)

    # 计算根节点轨迹
    velocities_root_xyz_no_heading = np.zeros((nfrm, 3))
    velocities_root_xyz_no_heading[:, [0, 2]] = velocities_root_xy_no_heading

    # 应用旋转到根节点速度
    for i in range(nfrm):
        velocities_root_xyz_no_heading[i] = np.matmul(velocities_root_xyz_no_heading[i], accumulated_rotations[i].T)

    # 累积根节点位置
    root_translation = np.cumsum(velocities_root_xyz_no_heading, axis=0)
    positions_with_heading[:, :, 0] += root_translation[:, 0:1]
    positions_with_heading[:, :, 2] += root_translation[:, 2:]

    return positions_with_heading

def create_motion_gif(joints_3d, save_path, fps=15, max_frames=60, title=""):
    """创建运动GIF动画"""
    # HumanML3D 22关节骨架连接关系
    # 关节顺序: 0-root, 1-pelvis, 2-spine1, 3-spine2, 4-spine3, 5-neck, 6-head,
    #          7-left_collar, 8-left_shoulder, 9-left_elbow, 10-left_wrist,
    #          11-right_collar, 12-right_shoulder, 13-right_elbow, 14-right_wrist,
    #          15-left_hip, 16-left_knee, 17-left_ankle, 18-left_foot,
    #          19-right_hip, 20-right_knee, 21-right_ankle
    skeleton_connections = [
        # 脊柱链
        (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
        # 左臂链
        (4, 7), (7, 8), (8, 9), (9, 10),
        # 右臂链
        (4, 11), (11, 12), (12, 13), (13, 14),
        # 左腿链
        (0, 15), (15, 16), (16, 17), (17, 18),
        # 右腿链
        (0, 19), (19, 20), (20, 21)
    ]
    
    fig = plt.figure(figsize=(12, 9))
    ax = fig.add_subplot(111, projection='3d')
    
    # 计算整个动画的坐标范围
    all_joints = joints_3d.reshape(-1, 3)
    max_range = np.array([all_joints[:, 0].max() - all_joints[:, 0].min(),
                         all_joints[:, 1].max() - all_joints[:, 1].min(),
                         all_joints[:, 2].max() - all_joints[:, 2].min()]).max() / 2.0
    
    # 防止范围过小
    if max_range < 0.1:
        max_range = 1.0
    
    mid_x = (all_joints[:, 0].max() + all_joints[:, 0].min()) * 0.5
    mid_y = (all_joints[:, 1].max() + all_joints[:, 1].min()) * 0.5
    mid_z = (all_joints[:, 2].max() + all_joints[:, 2].min()) * 0.5
    
    def animate(frame):
        ax.clear()
        frame_joints = joints_3d[frame]
        
        # 绘制关节点
        ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
                   c='red', s=60, alpha=0.8, edgecolors='darkred')
        
        # 绘制骨架连接
        for connection in skeleton_connections:
            if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
                start_joint = frame_joints[connection[0]]
                end_joint = frame_joints[connection[1]]
                ax.plot([start_joint[0], end_joint[0]], 
                       [start_joint[1], end_joint[1]], 
                       [start_joint[2], end_joint[2]], 'b-', linewidth=3, alpha=0.7)
        
        # 设置坐标轴
        ax.set_xlim(mid_x - max_range, mid_x + max_range)
        ax.set_ylim(mid_y - max_range, mid_y + max_range)
        ax.set_zlim(mid_z - max_range, mid_z + max_range)
        ax.set_xlabel('X', fontsize=12)
        ax.set_ylabel('Y', fontsize=12)
        ax.set_zlabel('Z', fontsize=12)
        ax.set_title(f'{title}\nFrame {frame+1}/{len(joints_3d)}', fontsize=14, pad=20)
        
        # 设置视角
        ax.view_init(elev=20, azim=frame * 2)  # 旋转视角
    
    # 创建动画
    num_frames = min(max_frames, len(joints_3d))
    ani = animation.FuncAnimation(fig, animate, frames=num_frames, interval=1000//fps, repeat=True)
    
    try:
        ani.save(save_path, writer='pillow', fps=fps)
        print(f"✓ 动画已保存到: {save_path}")
    except Exception as e:
        print(f"✗ 保存动画失败: {e}")
    
    plt.close()
    return ani

def load_text_description(motion_id):
    """加载对应的文本描述"""
    text_file = f"humanml3d_272/texts/{motion_id}.txt"
    if os.path.exists(text_file):
        try:
            with open(text_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                # 返回第一行作为主要描述
                return lines[0].strip() if lines else "No description"
        except:
            return "Failed to load description"
    return "No description available"

def plot_skeleton_frame(joints_3d, frame_idx=0, save_path=None, title=""):
    """绘制单帧3D骨架"""
    # 使用相同的HumanML3D骨架连接关系
    skeleton_connections = [
        # 脊柱链
        (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
        # 左臂链
        (4, 7), (7, 8), (8, 9), (9, 10),
        # 右臂链
        (4, 11), (11, 12), (12, 13), (13, 14),
        # 左腿链
        (0, 15), (15, 16), (16, 17), (17, 18),
        # 右腿链
        (0, 19), (19, 20), (20, 21)
    ]
    
    fig = plt.figure(figsize=(12, 10))
    ax = fig.add_subplot(111, projection='3d')
    
    # 获取指定帧的关节位置
    frame_joints = joints_3d[frame_idx]
    
    # 绘制关节点
    ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
               c='red', s=80, alpha=0.8, edgecolors='darkred')
    
    # 绘制骨架连接
    for connection in skeleton_connections:
        if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
            start_joint = frame_joints[connection[0]]
            end_joint = frame_joints[connection[1]]
            ax.plot([start_joint[0], end_joint[0]], 
                   [start_joint[1], end_joint[1]], 
                   [start_joint[2], end_joint[2]], 'b-', linewidth=3, alpha=0.7)
    
    # 设置坐标轴
    ax.set_xlabel('X', fontsize=12)
    ax.set_ylabel('Y', fontsize=12)
    ax.set_zlabel('Z', fontsize=12)
    ax.set_title(f'{title}\nFrame {frame_idx}', fontsize=14, pad=20)
    
    # 设置相同的坐标轴范围
    max_range = np.array([frame_joints[:, 0].max() - frame_joints[:, 0].min(),
                         frame_joints[:, 1].max() - frame_joints[:, 1].min(),
                         frame_joints[:, 2].max() - frame_joints[:, 2].min()]).max() / 2.0
    
    if max_range < 0.1:
        max_range = 1.0
    
    mid_x = (frame_joints[:, 0].max() + frame_joints[:, 0].min()) * 0.5
    mid_y = (frame_joints[:, 1].max() + frame_joints[:, 1].min()) * 0.5
    mid_z = (frame_joints[:, 2].max() + frame_joints[:, 2].min()) * 0.5
    
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 3D骨架图已保存到: {save_path}")

    plt.close()  # 关闭图形而不显示
    return fig

def main():
    print("🎬 快速动作演示系统启动中...")
    print("=" * 50)
    
    # 创建输出目录
    os.makedirs('demo_output', exist_ok=True)
    
    # 获取运动数据文件列表
    motion_files = glob("humanml3d_272/motion_data/*.npy")
    
    if not motion_files:
        print("❌ 未找到运动数据文件！")
        print("请确保 humanml3d_272/motion_data/ 目录中有 .npy 文件")
        return
    
    # 随机选择一些文件进行演示
    motion_files = sorted(motion_files)
    selected_files = random.sample(motion_files, min(8, len(motion_files)))
    
    print(f"📁 找到 {len(motion_files)} 个运动数据文件")
    print(f"🎯 随机选择 {len(selected_files)} 个文件进行演示")
    print()
    
    for i, motion_file in enumerate(selected_files):
        print(f"🎭 处理文件 {i+1}/{len(selected_files)}: {os.path.basename(motion_file)}")
        
        # 提取文件ID
        motion_id = os.path.basename(motion_file).replace('.npy', '')
        
        try:
            # 加载运动数据
            motion_data = np.load(motion_file)
            print(f"   📊 运动数据形状: {motion_data.shape}")
            
            # 加载文本描述
            text_desc = load_text_description(motion_id)
            print(f"   📝 文本描述: {text_desc}")
            
            # 转换为关节位置
            joints_3d = motion_to_joints(motion_data)
            print(f"   🦴 关节数据形状: {joints_3d.shape}")
            
            # 绘制第一帧的3D骨架
            save_path = f'demo_output/skeleton_{motion_id}_frame_0.png'
            plot_skeleton_frame(joints_3d, frame_idx=0, save_path=save_path, 
                              title=f"ID: {motion_id}\n{text_desc}")
            
            # 创建运动动画（如果序列足够长）
            if len(joints_3d) > 10:
                anim_path = f'demo_output/motion_{motion_id}.gif'
                create_motion_gif(joints_3d, save_path=anim_path, fps=15, max_frames=40, 
                                title=f"{text_desc}")
            else:
                print(f"   ⚠️  序列太短 ({len(joints_3d)} 帧)，跳过动画生成")
            
            print(f"   ✅ 完成处理: {motion_id}")
            print()
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            continue
    
    print("🎉 演示完成！")
    print(f"📂 可视化文件保存在 demo_output 目录中")
    print("=" * 50)
    
    # 显示生成的文件
    output_files = glob("demo_output/*")
    if output_files:
        print("📋 生成的文件列表:")
        for file in sorted(output_files):
            print(f"   • {os.path.basename(file)}")

if __name__ == "__main__":
    main()
