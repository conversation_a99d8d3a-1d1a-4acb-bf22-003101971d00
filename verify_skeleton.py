#!/usr/bin/env python3
"""
验证骨架结构是否正确
"""

import os
import numpy as np
import matplotlib
matplotlib.use('Agg')
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import torch
import torch.nn.functional as F

def rotation_6d_to_matrix(d6: torch.Tensor) -> torch.Tensor:
    """将6D旋转表示转换为旋转矩阵"""
    a1, a2 = d6[..., :3], d6[..., 3:]
    b1 = F.normalize(a1, dim=-1)
    b2 = a2 - (b1 * a2).sum(-1, keepdim=True) * b1
    b2 = F.normalize(b2, dim=-1)
    b3 = torch.cross(b1, b2, dim=-1)
    return torch.stack((b1, b2, b3), dim=-2)

def accumulate_rotations(relative_rotations):
    """累积旋转矩阵"""
    R_total = [relative_rotations[0]]
    for R_rel in relative_rotations[1:]:
        R_total.append(np.matmul(R_rel, R_total[-1]))
    return np.array(R_total)

def recover_from_local_position_correct(final_x, njoint=22):
    """正确的272维数据恢复函数"""
    if final_x.ndim == 3:
        bs, nfrm, _ = final_x.shape
        is_batched = True
    else:
        nfrm, _ = final_x.shape
        bs = 1
        is_batched = False
        final_x = final_x.reshape(1, *final_x.shape)

    positions_no_heading = final_x[:,:,8:8+3*njoint].reshape(bs, nfrm, njoint, 3) 
    velocities_root_xy_no_heading = final_x[:,:,:2] 
    global_heading_diff_rot = final_x[:,:,2:8] 

    positions_with_heading = []
    for b in range(bs):
        global_heading_rot = accumulate_rotations(
            rotation_6d_to_matrix(torch.from_numpy(global_heading_diff_rot[b])).numpy()
        )
        inv_global_heading_rot = np.transpose(global_heading_rot, (0, 2, 1))
        
        curr_pos_with_heading = np.matmul(
            np.repeat(inv_global_heading_rot[:, None,:, :], njoint, axis=1), 
            positions_no_heading[b][...,None]
        ).squeeze(-1)

        velocities_root_xyz_no_heading = np.zeros((velocities_root_xy_no_heading[b].shape[0], 3))
        velocities_root_xyz_no_heading[:, 0] = velocities_root_xy_no_heading[b, :, 0]
        velocities_root_xyz_no_heading[:, 2] = velocities_root_xy_no_heading[b, :, 1]
        velocities_root_xyz_no_heading[1:, :] = np.matmul(
            inv_global_heading_rot[:-1], 
            velocities_root_xyz_no_heading[1:, :,None]
        ).squeeze(-1)

        root_translation = np.cumsum(velocities_root_xyz_no_heading, axis=0)
        curr_pos_with_heading[:, :, 0] += root_translation[:, 0:1]
        curr_pos_with_heading[:, :, 2] += root_translation[:, 2:]
        
        positions_with_heading.append(curr_pos_with_heading)

    positions_with_heading = np.stack(positions_with_heading, axis=0)

    if not is_batched:
        positions_with_heading = positions_with_heading.squeeze(0)

    return positions_with_heading

def plot_skeleton_with_labels(joints_3d, frame_idx=0, save_path=None, title=""):
    """绘制带标签的骨架"""
    # HumanML3D 22关节名称
    joint_names = [
        "root", "pelvis", "spine1", "spine2", "spine3", "neck", "head",
        "left_collar", "left_shoulder", "left_elbow", "left_wrist",
        "right_collar", "right_shoulder", "right_elbow", "right_wrist",
        "left_hip", "left_knee", "left_ankle", "left_foot",
        "right_hip", "right_knee", "right_ankle"
    ]
    
    # 骨架连接关系
    skeleton_connections = [
        # 脊柱链
        (0, 1), (1, 2), (2, 3), (3, 4), (4, 5), (5, 6),
        # 左臂链
        (4, 7), (7, 8), (8, 9), (9, 10),
        # 右臂链  
        (4, 11), (11, 12), (12, 13), (13, 14),
        # 左腿链
        (0, 15), (15, 16), (16, 17), (17, 18),
        # 右腿链
        (0, 19), (19, 20), (20, 21)
    ]
    
    fig = plt.figure(figsize=(15, 12))
    ax = fig.add_subplot(111, projection='3d')
    
    frame_joints = joints_3d[frame_idx]
    
    # 绘制关节点
    ax.scatter(frame_joints[:, 0], frame_joints[:, 1], frame_joints[:, 2], 
               c='red', s=100, alpha=0.8, edgecolors='darkred')
    
    # 标注关节名称
    for i, (pos, name) in enumerate(zip(frame_joints, joint_names)):
        ax.text(pos[0], pos[1], pos[2], f'{i}:{name}', fontsize=8)
    
    # 绘制骨架连接
    for connection in skeleton_connections:
        if connection[0] < len(frame_joints) and connection[1] < len(frame_joints):
            start_joint = frame_joints[connection[0]]
            end_joint = frame_joints[connection[1]]
            ax.plot([start_joint[0], end_joint[0]], 
                   [start_joint[1], end_joint[1]], 
                   [start_joint[2], end_joint[2]], 'b-', linewidth=3, alpha=0.7)
    
    # 设置坐标轴
    ax.set_xlabel('X (meters)', fontsize=12)
    ax.set_ylabel('Y (meters)', fontsize=12)
    ax.set_zlabel('Z (meters)', fontsize=12)
    ax.set_title(f'{title}\nFrame {frame_idx} - 关节标注', fontsize=14, pad=20)
    
    # 设置合理的坐标轴范围
    max_range = 1.0
    mid_x = frame_joints[:, 0].mean()
    mid_y = frame_joints[:, 1].mean()
    mid_z = frame_joints[:, 2].mean()
    
    ax.set_xlim(mid_x - max_range, mid_x + max_range)
    ax.set_ylim(mid_y - max_range, mid_y + max_range)
    ax.set_zlim(mid_z - max_range, mid_z + max_range)
    
    # 设置视角
    ax.view_init(elev=15, azim=45)
    
    if save_path:
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        print(f"✓ 骨架验证图已保存: {save_path}")
    
    plt.close()

def verify_skeleton_structure():
    """验证骨架结构"""
    print("🔍 验证骨架结构")
    print("=" * 50)
    
    # 选择一个测试文件
    test_file = "humanml3d_272/motion_data/000005.npy"
    
    if not os.path.exists(test_file):
        print(f"❌ 测试文件不存在: {test_file}")
        return
    
    # 加载数据
    motion_data = np.load(test_file)
    print(f"📊 原始数据形状: {motion_data.shape}")
    
    # 加载标准化参数
    try:
        mean = np.load('humanml3d_272/mean_std/Mean.npy')
        std = np.load('humanml3d_272/mean_std/Std.npy')
        motion_data = motion_data * std + mean
        print(f"✓ 完成反标准化")
    except:
        print(f"⚠️  使用原始数据")
    
    # 恢复关节位置
    joints_3d = recover_from_local_position_correct(motion_data, njoint=22)
    print(f"🦴 关节数据形状: {joints_3d.shape}")
    
    # 分析关节位置
    print(f"\n📈 关节位置分析:")
    print(f"  X轴范围: [{joints_3d[:,:,0].min():.3f}, {joints_3d[:,:,0].max():.3f}] 米")
    print(f"  Y轴范围: [{joints_3d[:,:,1].min():.3f}, {joints_3d[:,:,1].max():.3f}] 米")
    print(f"  Z轴范围: [{joints_3d[:,:,2].min():.3f}, {joints_3d[:,:,2].max():.3f}] 米")
    
    # 检查关节间距离
    frame_0 = joints_3d[0]
    
    # 检查一些关键距离
    head_to_foot_dist = np.linalg.norm(frame_0[6] - frame_0[17])  # 头到左脚
    shoulder_width = np.linalg.norm(frame_0[8] - frame_0[12])     # 肩宽
    hip_width = np.linalg.norm(frame_0[15] - frame_0[19])        # 髋宽
    
    print(f"\n🔍 关键距离检查:")
    print(f"  头到脚距离: {head_to_foot_dist:.3f} 米")
    print(f"  肩宽: {shoulder_width:.3f} 米")
    print(f"  髋宽: {hip_width:.3f} 米")
    
    # 合理性检查
    reasonable = True
    if head_to_foot_dist < 1.2 or head_to_foot_dist > 2.2:
        print(f"  ⚠️  头到脚距离异常: {head_to_foot_dist:.3f}m (正常范围: 1.2-2.2m)")
        reasonable = False
    if shoulder_width < 0.3 or shoulder_width > 0.8:
        print(f"  ⚠️  肩宽异常: {shoulder_width:.3f}m (正常范围: 0.3-0.8m)")
        reasonable = False
    if hip_width < 0.2 or hip_width > 0.6:
        print(f"  ⚠️  髋宽异常: {hip_width:.3f}m (正常范围: 0.2-0.6m)")
        reasonable = False
    
    if reasonable:
        print(f"  ✅ 所有距离都在合理范围内")
    
    # 创建输出目录
    os.makedirs('skeleton_verification', exist_ok=True)
    
    # 绘制几个关键帧的骨架
    frames_to_check = [0, len(joints_3d)//4, len(joints_3d)//2, len(joints_3d)-1]
    
    for frame_idx in frames_to_check:
        save_path = f'skeleton_verification/skeleton_frame_{frame_idx}.png'
        plot_skeleton_with_labels(joints_3d, frame_idx=frame_idx, save_path=save_path, 
                                title=f"骨架验证 - 帧{frame_idx}")
    
    print(f"\n🎉 骨架结构验证完成！")
    print(f"📂 验证图片保存在 skeleton_verification 目录中")
    
    # 总结
    print(f"\n📋 验证总结:")
    print(f"  • 数据范围: {'✅ 正常' if reasonable else '❌ 异常'}")
    print(f"  • 关节数量: {'✅ 22个关节' if joints_3d.shape[1] == 22 else '❌ 关节数量错误'}")
    print(f"  • 数据完整性: {'✅ 无NaN值' if not np.isnan(joints_3d).any() else '❌ 包含NaN值'}")
    
    return reasonable

if __name__ == "__main__":
    verify_skeleton_structure()
